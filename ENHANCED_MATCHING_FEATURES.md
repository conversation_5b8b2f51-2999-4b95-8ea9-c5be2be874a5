# 验证码增强匹配功能

## 功能概述

本次更新为验证码扩展增加了以下三个主要功能：

1. **自动填入当前页面对应的验证码**
2. **在列表中筛选符合当前页面的验证码并显示，隐藏其他不相关的验证码**
3. **页面匹配的规则是模糊匹配**

## 实现的功能

### 1. 增强的模糊匹配算法

- **文件**: `src/utils.ts`
- **函数**: `isMatchedEntry()`, `isHostMatched()`

#### 匹配规则：
- 支持完整域名匹配：`<EMAIL>` 匹配 `kfms-pre-api.bbadev.cc`
- 支持主域名匹配：`example.com` 匹配 `admin.example.com`
- 支持Email格式匹配：`<EMAIL>` 匹配 `mail.google.com`
- 支持issuer和account字段的双重匹配
- 支持域名关键词匹配

### 2. 智能自动填充

- **文件**: `src/background.ts`
- **函数**: `selectBestMatchedEntry()`, `calculateMatchScore()`

#### 优先级算法：
- 置顶(pinned)验证码：+100分
- 精确域名匹配(::分隔)：+80分
- issuer完整域名匹配：+50分
- account完整域名匹配：+40分
- issuer主域名匹配：+30分
- account主域名匹配：+20分

### 3. 实时页面监听

- **文件**: `src/popup.ts`
- **功能**: 监听窗口焦点变化和定期更新siteName

#### 监听机制：
- 窗口焦点变化时更新匹配状态
- 每2秒定期检查页面变化
- 实时更新验证码列表过滤状态

### 4. 智能过滤显示

- **文件**: `src/components/Popup/MainBody.vue`, `src/store/Accounts.ts`
- **功能**: 根据匹配状态显示/隐藏验证码条目

#### 过滤逻辑：
- 显示匹配当前页面的验证码
- 始终显示置顶的验证码
- 隐藏不相关的验证码
- 支持搜索功能覆盖过滤

## 修改的文件列表

1. **src/utils.ts**
   - 增强 `isMatchedEntry()` 函数
   - 新增 `isHostMatched()` 函数

2. **src/background.ts**
   - 改进 `autofill` 命令处理
   - 新增 `selectBestMatchedEntry()` 函数
   - 新增 `calculateMatchScore()` 函数

3. **src/popup.ts**
   - 添加页面变化监听
   - 实时更新siteName

4. **src/store/Accounts.ts**
   - 新增 `updateSiteName` mutation

5. **src/components/Popup/EntryComponent.vue**
   - 支持 `filtered` 和 `notSearched` 属性

## 使用方法

### 自动填充
1. 在需要输入验证码的页面上
2. 使用快捷键 `Ctrl+Shift+A`（或扩展设置中配置的快捷键）
3. 系统会自动选择最匹配的验证码并填入

### 智能过滤
1. 打开验证码扩展弹窗
2. 系统会自动显示与当前页面相关的验证码
3. 不相关的验证码会被隐藏
4. 置顶的验证码始终显示

## 测试用例

### 测试用例1：完整域名匹配
- **当前页面**: `kfms-pre-api.bbadev.cc`
- **验证码备注**: `<EMAIL>`
- **预期结果**: 匹配成功

### 测试用例2：子域名匹配
- **当前页面**: `admin.example.com`
- **验证码备注**: `example.com`
- **预期结果**: 匹配成功

### 测试用例3：主域名匹配
- **当前页面**: `www.github.com`
- **验证码备注**: `GitHub`
- **预期结果**: 匹配成功

### 测试用例4：Email格式匹配
- **当前页面**: `mail.google.com`
- **验证码备注**: `<EMAIL>`
- **预期结果**: 匹配成功

## 技术细节

### 匹配算法
```typescript
// 检查域名模糊匹配
function isHostMatched(entryText: string, siteHost: string): boolean {
  // 直接包含匹配
  // 主域名匹配
  // 域名关键词匹配
}
```

### 评分系统
```typescript
// 计算匹配分数
function calculateMatchScore(entry, siteHost, siteNameFromHost): number {
  // 置顶优先级
  // 域名匹配分数
  // issuer/account字段分数
}
```

## 兼容性

- 保持与现有功能的完全兼容
- 不影响原有的匹配逻辑
- 增强而非替换现有功能

## 注意事项

1. 模糊匹配可能会产生误匹配，建议用户合理设置验证码的issuer和account字段
2. 置顶功能可以确保重要的验证码始终显示
3. 搜索功能可以覆盖智能过滤，手动查找特定验证码
