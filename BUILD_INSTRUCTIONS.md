# 构建和安装说明

## 🚀 快速构建

### Windows 用户

我已经为您创建了Windows兼容的构建脚本。您可以使用以下任一方法：

#### 方法1：使用批处理文件（推荐）
```cmd
.\build-windows.bat chrome
```

#### 方法2：使用PowerShell脚本
```powershell
.\build-windows.ps1 chrome
```

### 支持的平台
- `chrome` - Chrome浏览器
- `firefox` - Firefox浏览器  
- `edge` - Microsoft Edge浏览器

## 📋 详细构建步骤

### 1. 环境准备
确保您已经安装了：
- Node.js (推荐版本 16+)
- npm (通常随Node.js一起安装)

### 2. 安装依赖
```cmd
npm install
```

### 3. 构建扩展
```cmd
# 构建Chrome版本
.\build-windows.bat chrome

# 构建Firefox版本
.\build-windows.bat firefox

# 构建Edge版本
.\build-windows.bat edge
```

### 4. 构建输出
构建完成后，您会在对应的文件夹中找到扩展文件：
- `chrome/` - Chrome扩展文件
- `firefox/` - Firefox扩展文件
- `edge/` - Edge扩展文件

## 🔧 安装扩展

### Chrome/Edge 安装步骤

1. **打开扩展管理页面**
   - Chrome: 访问 `chrome://extensions/`
   - Edge: 访问 `edge://extensions/`

2. **启用开发者模式**
   - 在页面右上角找到"开发者模式"开关
   - 点击开关启用开发者模式

3. **加载扩展**
   - 点击"加载已解压的扩展程序"按钮
   - 选择构建生成的文件夹（如 `chrome/` 文件夹）
   - 点击"选择文件夹"

4. **验证安装**
   - 扩展应该出现在扩展列表中
   - 浏览器工具栏应该显示扩展图标

### Firefox 安装步骤

1. **打开附加组件管理页面**
   - 访问 `about:addons`

2. **临时安装**
   - 点击齿轮图标 → "调试附加组件"
   - 点击"临时载入附加组件"
   - 选择 `firefox/manifest.json` 文件

## ⚙️ 配置快捷键

安装完成后，建议配置快捷键以便快速使用：

### Chrome/Edge
1. 访问 `chrome://extensions/shortcuts/` 或 `edge://extensions/shortcuts/`
2. 找到"Authenticator"扩展
3. 为"Autofill the matched code"设置快捷键（推荐：`Ctrl+Shift+A`）

### Firefox
1. 访问 `about:addons`
2. 点击齿轮图标 → "管理扩展快捷键"
3. 为相应功能设置快捷键

## 🧪 测试功能

### 1. 基本功能测试
1. 点击扩展图标打开弹窗
2. 添加一个测试验证码
3. 验证验证码是否正常显示和更新

### 2. 增强匹配功能测试
1. 打开测试页面：`test_enhanced_matching.html`
2. 添加验证码，issuer设置为：`<EMAIL>`
3. 访问包含 `kfms-pre-api.bbadev.cc` 的网站
4. 打开扩展弹窗，验证是否只显示匹配的验证码

### 3. 自动填充测试
1. 在需要验证码的页面上
2. 使用配置的快捷键（如 `Ctrl+Shift+A`）
3. 验证是否自动填入了正确的验证码

## 🐛 故障排除

### 构建失败
如果构建失败，请检查：
1. Node.js版本是否正确（推荐16+）
2. 是否正确安装了所有依赖：`npm install`
3. 是否有权限问题（尝试以管理员身份运行）

### 扩展无法加载
如果扩展无法加载，请检查：
1. 是否启用了开发者模式
2. 选择的文件夹是否正确（应该包含manifest.json）
3. 浏览器控制台是否有错误信息

### 功能不工作
如果增强功能不工作，请检查：
1. 扩展是否有必要的权限
2. 验证码的issuer/account字段是否正确设置
3. 浏览器控制台是否有JavaScript错误

## 📝 开发说明

### 修改代码后重新构建
如果您修改了源代码，需要重新构建：
```cmd
.\build-windows.bat chrome
```

然后在浏览器扩展管理页面点击"重新加载"按钮。

### 调试
- 使用浏览器开发者工具调试popup页面
- 在扩展管理页面点击"背景页"调试background script
- 在网页上按F12调试content script

## 🎯 新功能说明

本次更新增加了以下功能：

1. **智能匹配算法** - 支持模糊域名匹配
2. **自动填充增强** - 智能选择最佳匹配的验证码
3. **实时过滤** - 根据当前页面自动过滤显示相关验证码

详细功能说明请参考 `ENHANCED_MATCHING_FEATURES.md` 文件。
