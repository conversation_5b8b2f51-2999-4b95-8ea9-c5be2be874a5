name: Release

on:
  push:
    tags: 
      - 'v*'

jobs:
  release:
    runs-on: ubuntu-latest
    name: Publish release build
    
    steps:
      - uses: actions/checkout@v2
      
      - name: Setup Node.js environment
        uses: actions/setup-node@v2.1.2
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build
        run: bash scripts/release.sh
        env:
          CREDS_FILE_PASSWORD: ${{ secrets.CREDS_FILE_PASSWORD }}
      
      - name: Create a release
        id: create_release
        uses: actions/create-release@v1.1.4
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref }}
          release_name: Release ${{ github.ref }}
          draft: false
          prerelease: false
      - name: Upload Release Asset
        id: upload-release-asset 
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: ./release.tar.gz
          asset_name: release.tar.gz
          asset_content_type: application/gzip
