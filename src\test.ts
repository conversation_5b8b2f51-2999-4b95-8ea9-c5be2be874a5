import "mocha";
import { <PERSON><PERSON><PERSON><PERSON>ort<PERSON> } from "./mochaReporter";
import sinon from "sinon";

// @ts-expect-error this is not a node require
const tests = require.context("./test", true, /\.tsx?$/);
tests.keys().forEach(tests);

mocha.setup({
  // @ts-expect-error - typings are wrong
  reporter: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  rootHooks: {
    afterEach() {
      sinon.restore();
    },
  },
});

mocha.run();
