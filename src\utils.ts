export async function getSiteName() {
  const tab = await getCurrentTab();
  const query = new URLSearchParams(document.location.search.substring(1));

  let title: string | null;
  let url: string | null;
  const titleFromQuery = query.get("title");
  const urlFromQuery = query.get("url");

  if (titleFromQuery && urlFromQuery) {
    title = decodeURIComponent(titleFromQuery);
    url = decodeURIComponent(urlFromQuery);
  } else {
    if (!tab) {
      return [null, null];
    }

    title = tab.title?.replace(/[^a-z0-9]/gi, "").toLowerCase() ?? null;
    url = tab.url ?? null;
  }

  if (!url) {
    return [title, null];
  }

  const urlParser = new URL(url);
  const hostname = urlParser.hostname; // it's always lower case

  // try to parse name from hostname
  // i.e. hostname is www.example.com
  // name should be example
  let nameFromDomain = "";

  // ip address
  if (/^\d+\.\d+\.\d+\.\d+$/.test(hostname)) {
    nameFromDomain = hostname;
  }

  // local network
  if (hostname.indexOf(".") === -1) {
    nameFromDomain = hostname;
  }

  const hostLevelUnits = hostname.split(".");

  if (hostLevelUnits.length === 2) {
    nameFromDomain = hostLevelUnits[0];
  }

  // www.example.com
  // example.com.cn
  if (hostLevelUnits.length > 2) {
    // example.com.cn
    if (
      ["com", "net", "org", "edu", "gov", "co"].indexOf(
        hostLevelUnits[hostLevelUnits.length - 2]
      ) !== -1
    ) {
      nameFromDomain = hostLevelUnits[hostLevelUnits.length - 3];
    } else {
      // www.example.com
      nameFromDomain = hostLevelUnits[hostLevelUnits.length - 2];
    }
  }

  nameFromDomain = nameFromDomain.replace(/-/g, "").toLowerCase();

  return [title, nameFromDomain, hostname];
}

export function getMatchedEntries(
  siteName: Array<string | null>,
  entries: OTPEntryInterface[]
) {
  if (siteName.length < 2) {
    return false;
  }

  const matched = [];

  for (const entry of entries) {
    if (isMatchedEntry(siteName, entry)) {
      matched.push(entry);
    }
  }

  return matched;
}

export function getMatchedEntriesHash(
  siteName: Array<string | null>,
  entries: OTPEntryInterface[]
) {
  const matchedEnteries = getMatchedEntries(siteName, entries);
  if (matchedEnteries) {
    return matchedEnteries.map((entry) => entry.hash);
  }

  return false;
}

function isMatchedEntry(
  siteName: Array<string | null>,
  entry: OTPEntryInterface
) {
  if (!entry.issuer && !entry.account) {
    return false;
  }

  const siteTitle = siteName[0] || "";
  const siteNameFromHost = siteName[1] || "";
  const siteHost = siteName[2] || "";

  // 检查issuer字段的匹配
  if (entry.issuer) {
    const issuerHostMatches = entry.issuer.split("::");
    const issuer = issuerHostMatches[0].replace(/[^0-9a-z]/gi, "").toLowerCase();

    if (issuer) {
      // 原有的精确匹配逻辑
      if (issuerHostMatches.length > 1) {
        if (siteHost && siteHost.indexOf(issuerHostMatches[1]) !== -1) {
          return true;
        }
      }

      if (siteTitle && siteTitle.indexOf(issuer) !== -1) {
        return true;
      }

      if (siteNameFromHost && issuer.indexOf(siteNameFromHost) !== -1) {
        return true;
      }

      // 新增：模糊匹配逻辑 - 检查issuer是否包含当前域名的关键部分
      if (siteHost && isHostMatched(entry.issuer, siteHost)) {
        return true;
      }
    }
  }

  // 检查account字段的匹配
  if (entry.account) {
    // 模糊匹配account字段，支持email格式和域名格式
    if (siteHost && isHostMatched(entry.account, siteHost)) {
      return true;
    }

    // 检查account是否包含站点名称
    const accountLower = entry.account.toLowerCase();
    if (siteNameFromHost && accountLower.indexOf(siteNameFromHost) !== -1) {
      return true;
    }
  }

  return false;
}

// 新增：域名模糊匹配函数
function isHostMatched(entryText: string, siteHost: string): boolean {
  if (!entryText || !siteHost) {
    return false;
  }

  const entryLower = entryText.toLowerCase();
  const hostLower = siteHost.toLowerCase();

  // 直接包含匹配
  if (entryLower.indexOf(hostLower) !== -1) {
    return true;
  }

  // 提取域名部分进行匹配
  const hostParts = hostLower.split('.');
  const entryParts = entryLower.split(/[@.\s]/);

  // 检查主域名匹配（去掉子域名）
  if (hostParts.length >= 2) {
    const mainDomain = hostParts.slice(-2).join('.');
    if (entryLower.indexOf(mainDomain) !== -1) {
      return true;
    }
  }

  // 检查域名关键词匹配
  for (const hostPart of hostParts) {
    if (hostPart.length > 3) { // 只检查长度大于3的部分，避免匹配com、org等
      for (const entryPart of entryParts) {
        if (entryPart.length > 3 &&
            (entryPart.indexOf(hostPart) !== -1 || hostPart.indexOf(entryPart) !== -1)) {
          return true;
        }
      }
    }
  }

  return false;
}

export async function getCurrentTab() {
  const currentWindow = await chrome.windows.getCurrent();
  const queryOptions = { active: true, windowId: currentWindow.id };
  // `tab` will either be a `tabs.Tab` instance or `undefined`.
  const [tab] = await chrome.tabs.query(queryOptions);
  return tab;
}

interface TabWithIdAndURL extends chrome.tabs.Tab {
  id: number;
  url: string;
}

export function okToInjectContentScript(
  tab: chrome.tabs.Tab
): tab is TabWithIdAndURL {
  return (
    tab.id !== undefined &&
    tab.url !== undefined &&
    (tab.url.startsWith("https://") ||
      tab.url.startsWith("http://") ||
      tab.url.startsWith("file://"))
  );
}
