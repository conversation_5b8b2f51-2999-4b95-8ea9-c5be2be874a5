export async function getSiteName() {
  const tab = await getCurrentTab();
  const query = new URLSearchParams(document.location.search.substring(1));

  let title: string | null;
  let url: string | null;
  const titleFromQuery = query.get("title");
  const urlFromQuery = query.get("url");

  if (titleFromQuery && urlFromQuery) {
    title = decodeURIComponent(titleFromQuery);
    url = decodeURIComponent(urlFromQuery);
  } else {
    if (!tab) {
      return [null, null];
    }

    title = tab.title?.replace(/[^a-z0-9]/gi, "").toLowerCase() ?? null;
    url = tab.url ?? null;
  }

  if (!url) {
    return [title, null];
  }

  const urlParser = new URL(url);
  const hostname = urlParser.hostname; // it's always lower case

  // try to parse name from hostname
  // i.e. hostname is www.example.com
  // name should be example
  let nameFromDomain = "";

  // ip address
  if (/^\d+\.\d+\.\d+\.\d+$/.test(hostname)) {
    nameFromDomain = hostname;
  }

  // local network
  if (hostname.indexOf(".") === -1) {
    nameFromDomain = hostname;
  }

  const hostLevelUnits = hostname.split(".");

  if (hostLevelUnits.length === 2) {
    nameFromDomain = hostLevelUnits[0];
  }

  // www.example.com
  // example.com.cn
  if (hostLevelUnits.length > 2) {
    // example.com.cn
    if (
      ["com", "net", "org", "edu", "gov", "co"].indexOf(
        hostLevelUnits[hostLevelUnits.length - 2]
      ) !== -1
    ) {
      nameFromDomain = hostLevelUnits[hostLevelUnits.length - 3];
    } else {
      // www.example.com
      nameFromDomain = hostLevelUnits[hostLevelUnits.length - 2];
    }
  }

  nameFromDomain = nameFromDomain.replace(/-/g, "").toLowerCase();

  return [title, nameFromDomain, hostname];
}

export function getMatchedEntries(
  siteName: Array<string | null>,
  entries: OTPEntryInterface[]
) {
  if (siteName.length < 2) {
    return false;
  }

  const matched = [];

  for (const entry of entries) {
    if (isMatchedEntry(siteName, entry)) {
      matched.push(entry);
    }
  }

  return matched;
}

export function getMatchedEntriesHash(
  siteName: Array<string | null>,
  entries: OTPEntryInterface[]
) {
  const matchedEnteries = getMatchedEntries(siteName, entries);
  if (matchedEnteries) {
    return matchedEnteries.map((entry) => entry.hash);
  }

  return false;
}

function isMatchedEntry(
  siteName: Array<string | null>,
  entry: OTPEntryInterface
) {
  if (!entry.issuer && !entry.account) {
    return false;
  }

  const siteTitle = siteName[0] || "";
  const siteNameFromHost = siteName[1] || "";
  const siteHost = siteName[2] || "";

  // 检查issuer字段的匹配
  if (entry.issuer) {
    const issuerHostMatches = entry.issuer.split("::");
    const issuer = issuerHostMatches[0].replace(/[^0-9a-z]/gi, "").toLowerCase();

    if (issuer) {
      // 原有的精确匹配逻辑
      if (issuerHostMatches.length > 1) {
        if (siteHost && siteHost.indexOf(issuerHostMatches[1]) !== -1) {
          return true;
        }
      }

      if (siteTitle && siteTitle.indexOf(issuer) !== -1) {
        return true;
      }

      if (siteNameFromHost && issuer.indexOf(siteNameFromHost) !== -1) {
        return true;
      }

      // 精确匹配逻辑 - 检查issuer是否包含完全匹配的域名
      if (siteHost && isExactHostMatched(entry.issuer, siteHost)) {
        return true;
      }
    }
  }

  // 检查account字段的匹配
  if (entry.account) {
    // 精确匹配account字段
    if (siteHost && isExactHostMatched(entry.account, siteHost)) {
      return true;
    }

    // 检查account是否包含站点名称
    const accountLower = entry.account.toLowerCase();
    if (siteNameFromHost && accountLower.indexOf(siteNameFromHost) !== -1) {
      return true;
    }
  }

  return false;
}

// 精确域名匹配函数
function isExactHostMatched(entryText: string, siteHost: string): boolean {
  if (!entryText || !siteHost) {
    return false;
  }

  const entryLower = entryText.toLowerCase();
  const hostLower = siteHost.toLowerCase();

  // 从entryText中提取域名部分
  // 支持格式：<EMAIL>, domain.com, https://domain.com等
  const domainRegex = /(?:@|:\/\/)?([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g;
  const matches = entryLower.match(domainRegex);

  if (matches) {
    for (const match of matches) {
      // 清理匹配结果，移除@和://前缀
      const cleanDomain = match.replace(/^(@|:\/\/)/, '');

      // 精确匹配：域名必须完全一致
      if (cleanDomain === hostLower) {
        return true;
      }
    }
  }

  // 如果没有找到域名格式，检查是否直接包含完整域名
  // 使用单词边界确保精确匹配
  const exactMatchRegex = new RegExp('\\b' + hostLower.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '\\b', 'i');
  return exactMatchRegex.test(entryLower);
}

export async function getCurrentTab() {
  const currentWindow = await chrome.windows.getCurrent();
  const queryOptions = { active: true, windowId: currentWindow.id };
  // `tab` will either be a `tabs.Tab` instance or `undefined`.
  const [tab] = await chrome.tabs.query(queryOptions);
  return tab;
}

interface TabWithIdAndURL extends chrome.tabs.Tab {
  id: number;
  url: string;
}

export function okToInjectContentScript(
  tab: chrome.tabs.Tab
): tab is TabWithIdAndURL {
  return (
    tab.id !== undefined &&
    tab.url !== undefined &&
    (tab.url.startsWith("https://") ||
      tab.url.startsWith("http://") ||
      tab.url.startsWith("file://"))
  );
}
