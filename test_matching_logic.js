// 测试精确匹配逻辑
function isExactHostMatched(entryText, siteHost) {
  if (!entryText || !siteHost) {
    return false;
  }

  const entryLower = entryText.toLowerCase();
  const hostLower = siteHost.toLowerCase();

  // 从entryText中提取域名部分
  // 支持格式：<EMAIL>, domain.com, https://domain.com等
  const domainRegex = /(?:@|:\/\/)?([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g;
  const matches = entryLower.match(domainRegex);
  
  if (matches) {
    for (const match of matches) {
      // 清理匹配结果，移除@和://前缀
      const cleanDomain = match.replace(/^(@|:\/\/)/, '');
      
      // 精确匹配：域名必须完全一致
      if (cleanDomain === hostLower) {
        return true;
      }
    }
  }

  // 如果没有找到域名格式，检查是否直接包含完整域名
  // 使用单词边界确保精确匹配
  const exactMatchRegex = new RegExp('\\b' + hostLower.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') + '\\b', 'i');
  return exactMatchRegex.test(entryLower);
}

// 测试用例
const testCases = [
  {
    name: "dev-goc.bbadev.cc 测试",
    entryText: "<EMAIL>",
    siteHost: "dev-goc.bbadev.cc",
    expected: true
  },
  {
    name: "kfms-pre-api.bbadev.cc 测试",
    entryText: "<EMAIL>",
    siteHost: "kfms-pre-api.bbadev.cc",
    expected: true
  },
  {
    name: "GitHub 测试",
    entryText: "<EMAIL>",
    siteHost: "github.com",
    expected: true
  },
  {
    name: "子域名不匹配测试",
    entryText: "example.com",
    siteHost: "admin.example.com",
    expected: false
  },
  {
    name: "不同域名测试",
    entryText: "<EMAIL>",
    siteHost: "google.com",
    expected: false
  },
  {
    name: "直接域名匹配",
    entryText: "example.com",
    siteHost: "example.com",
    expected: true
  },
  {
    name: "HTTPS URL 测试",
    entryText: "https://api.example.com",
    siteHost: "api.example.com",
    expected: true
  }
];

// 运行测试
console.log("=== 精确匹配逻辑测试 ===\n");

let passedTests = 0;
let totalTests = testCases.length;

testCases.forEach((testCase, index) => {
  const result = isExactHostMatched(testCase.entryText, testCase.siteHost);
  const passed = result === testCase.expected;
  
  console.log(`测试 ${index + 1}: ${testCase.name}`);
  console.log(`  验证码文本: "${testCase.entryText}"`);
  console.log(`  页面域名: "${testCase.siteHost}"`);
  console.log(`  预期结果: ${testCase.expected}`);
  console.log(`  实际结果: ${result}`);
  console.log(`  测试状态: ${passed ? '✅ 通过' : '❌ 失败'}`);
  console.log('');
  
  if (passed) {
    passedTests++;
  }
});

console.log(`=== 测试总结 ===`);
console.log(`通过: ${passedTests}/${totalTests}`);
console.log(`成功率: ${(passedTests / totalTests * 100).toFixed(1)}%`);

// 特别测试您提到的用例
console.log(`\n=== 特别测试：您的用例 ===`);
const yourCase = isExactHostMatched("<EMAIL>", "dev-goc.bbadev.cc");
console.log(`验证码: "<EMAIL>"`);
console.log(`页面: "dev-goc.bbadev.cc"`);
console.log(`结果: ${yourCase ? '✅ 匹配成功' : '❌ 匹配失败'}`);

// 导出函数供浏览器使用
if (typeof window !== 'undefined') {
  window.isExactHostMatched = isExactHostMatched;
  window.testMatchingLogic = () => {
    testCases.forEach((testCase, index) => {
      const result = isExactHostMatched(testCase.entryText, testCase.siteHost);
      const passed = result === testCase.expected;
      console.log(`${testCase.name}: ${passed ? '✅' : '❌'}`);
    });
  };
}
