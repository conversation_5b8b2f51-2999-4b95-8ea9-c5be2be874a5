// Re-usable ui components

// Colors
// go from darkest to lightest

$themes: (
  normal: (
    black-1: black,
    black-transparent: rgba(0, 0, 0, 0.5),
    white-1: white,
    white-transparent: rgba(255, 255, 255, 0.5),
    grey-1: grey,
    grey-2: #ccc,
    grey-3: #eee,
    grey-background: #eee,
    blue-1: #08c,
    yellow-1: #fff1ba,
    yellow-2: #fff4cc,
    red-1: #dd4b39,
    red-2: #eea59c,
    black-search: #2a2a2e,
    white-search: #f9f9fa,
    grey-search: #b1b1b3,
    blue-menu: #f4fcff,
  ),
  accessibility: (
    black-1: white,
    black-transparent: rgba(255, 255, 255, 1),
    white-1: black,
    white-transparent: rgba(0, 0, 0, 0.5),
    grey-1: white,
    grey-2: white,
    grey-3: white,
    grey-background: black,
    blue-1: yellow,
    yellow-1: yellow,
    yellow-2: yellow,
    red-1: red,
    red-2: red,
    black-search: white,
    white-search: black,
    grey-search: white,
    blue-menu: cyan,
  ),
  dark: (
    black-1: #ccc,
    black-transparent: rgba(255, 255, 255, 0.5),
    white-1: #242424,
    white-transparent: rgba(0, 0, 0, 0.5),
    grey-1: grey,
    grey-2: rgba(255, 255, 255, 0.15),
    grey-3: #444,
    grey-background: #1e1e1e,
    blue-1: white,
    yellow-1: rgba(255, 255, 255, 0.5),
    yellow-2: rgba(255, 255, 255, 0.35),
    red-1: #dd4b39,
    red-2: #61221a,
    black-search: white,
    white-search: #202020,
    grey-search: rgba(255, 255, 255, 0.35),
    blue-menu: #2a2d2e,
  ),
  simple: (
    black-1: black,
    black-transparent: rgba(0, 0, 0, 0.5),
    white-1: white,
    white-transparent: rgba(255, 255, 255, 0.5),
    grey-1: grey,
    grey-2: #ccc,
    grey-3: #eee,
    grey-background: #fff,
    blue-1: #08c,
    yellow-1: #fff1ba,
    yellow-2: #fff4cc,
    red-1: #dd4b39,
    red-2: #eea59c,
    black-search: #2a2a2e,
    white-search: #f9f9fa,
    grey-search: #b1b1b3,
    blue-menu: #f4fcff,
  ),
  compact: (
    black-1: black,
    black-transparent: rgba(0, 0, 0, 0.5),
    white-1: white,
    white-transparent: rgba(255, 255, 255, 0.5),
    grey-1: grey,
    grey-2: #ccc,
    grey-3: #eee,
    grey-background: #fff,
    blue-1: #08c,
    yellow-1: #fff1ba,
    yellow-2: #fff4cc,
    red-1: #dd4b39,
    red-2: #eea59c,
    black-search: #2a2a2e,
    white-search: #f9f9fa,
    grey-search: #b1b1b3,
    blue-menu: #f4fcff,
  ),
  flat: (
    black-1: black,
    black-transparent: rgba(0, 0, 0, 0.5),
    white-1: white,
    white-transparent: rgba(255, 255, 255, 0.5),
    grey-1: grey,
    grey-2: #ccc,
    grey-3: #eee,
    grey-background: #eee,
    blue-1: #08c,
    yellow-1: #fff1ba,
    yellow-2: #fff4cc,
    red-1: #dd4b39,
    red-2: #eea59c,
    black-search: #2a2a2e,
    white-search: #f9f9fa,
    grey-search: #b1b1b3,
    blue-menu: #f4fcff,
  ),
);

$theme-map: null;

@mixin themify($themes: $themes) {
  @each $theme, $map in $themes {
    .theme-#{$theme} & {
      $theme-map: () !global;
      @each $key, $submap in $map {
        $value: map-get(map-get($themes, $theme), "#{$key}");
        $theme-map: map-merge(
          $theme-map,
          (
            $key: $value,
          )
        ) !global;
      }
      @content;
      $theme-map: null !global;
    }
  }
}

@function themed($key) {
  @return map-get($theme-map, $key);
}

// Shared
@mixin hover-black {
  &:hover {
    svg {
      @include themify($themes) {
        fill: themed("black-1");
      }
    }
  }
}

@mixin icon-special($size, $color) {
  svg {
    vertical-align: middle;
    fill: $color;
    height: $size;
    width: $size;
  }
}

// Classes
.button {
  margin: 10px;
  padding: 20px;
  border-radius: 2px;
  position: relative;
  text-align: center;
  font-size: 16px;
  width: -moz-available;
  width: -webkit-fill-available;
  @include themify($themes) {
    background: themed("white-1");
    border: themed("grey-2") 1px solid;
    color: themed("grey-1");
  }
  cursor: pointer;

  &:hover {
    @include themify($themes) {
      color: themed("black-1");
    }
  }
}

.button-small {
  @extend .button;
  font-size: 12px;
  margin: 20px 100px;
  padding: 10px;
}

.input {
  display: block;
  margin: 15px 10px 20px 10px;
  padding: 5px 10px;
  width: 260px;
  border: none;
  @include themify($themes) {
    color: themed("black-1");
    border-bottom: themed("black-1") 1px solid;
    background: themed("grey-3");
  }
  outline: none;
}

a {
  @include themify($themes) {
    color: themed("blue-1");
  }
}
