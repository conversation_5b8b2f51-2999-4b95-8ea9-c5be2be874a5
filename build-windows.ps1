# Windows PowerShell build script for Authenticator Extension
# Usage: .\build-windows.ps1 [chrome|firefox|edge]

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("chrome", "firefox", "edge")]
    [string]$Platform = "chrome"
)

Write-Host "Building Authenticator Extension for $Platform..." -ForegroundColor Green

# Clean old build files
Write-Host "Cleaning old build files..." -ForegroundColor Yellow
if (Test-Path "build") { Remove-Item -Recurse -Force "build" }
if (Test-Path "dist") { Remove-Item -Recurse -Force "dist" }
if (Test-Path "css") { Remove-Item -Recurse -Force "css" }
if (Test-Path "chrome") { Remove-Item -Recurse -Force "chrome" }
if (Test-Path "firefox") { Remove-Item -Recurse -Force "firefox" }
if (Test-Path "edge") { Remove-Item -Recurse -Force "edge" }

# Check if credentials are set (warning only)
$credContent = Get-Content "src\models\credentials.ts" -Raw
if ($credContent -match 'client_id: ""') {
    Write-Host "Warning: Missing API credentials in credentials.ts" -ForegroundColor Yellow
    Write-Host "This is OK for development, but you'll need real credentials for production" -ForegroundColor Yellow
}

# Run webpack
Write-Host "Running webpack..." -ForegroundColor Yellow
try {
    & npx webpack --config webpack.config.js
    if ($LASTEXITCODE -ne 0) {
        throw "Webpack build failed"
    }
} catch {
    Write-Host "Error: Webpack build failed: $_" -ForegroundColor Red
    exit 1
}

# Compile SASS
Write-Host "Compiling SASS..." -ForegroundColor Yellow
try {
    & npx sass sass:css
    if ($LASTEXITCODE -ne 0) {
        throw "SASS compilation failed"
    }
} catch {
    Write-Host "Error: SASS compilation failed: $_" -ForegroundColor Red
    exit 1
}

# Copy additional CSS files
if (Test-Path "css") {
    Copy-Item "sass\DroidSansMono.woff2" "css\" -Force
    Copy-Item "sass\mocha.css" "css\" -Force
}

# Create platform-specific build
Write-Host "Creating $Platform build..." -ForegroundColor Yellow

# Create platform directory
New-Item -ItemType Directory -Path $Platform -Force | Out-Null

# Copy core files
$filesToCopy = @("dist", "css", "images", "_locales", "LICENSE", "view")
foreach ($item in $filesToCopy) {
    if (Test-Path $item) {
        Copy-Item $item $Platform -Recurse -Force
        Write-Host "Copied $item" -ForegroundColor Gray
    } else {
        Write-Host "Warning: $item not found" -ForegroundColor Yellow
    }
}

# Copy appropriate manifest
$manifestFile = "manifests\manifest-$Platform.json"
if (Test-Path $manifestFile) {
    Copy-Item $manifestFile "$Platform\manifest.json" -Force
    Write-Host "Copied manifest for $Platform" -ForegroundColor Gray
} else {
    Write-Host "Error: Manifest file $manifestFile not found" -ForegroundColor Red
    exit 1
}

# Copy schema for Chrome/Edge
if ($Platform -eq "chrome" -or $Platform -eq "edge") {
    if (Test-Path "manifests\schema-chrome.json") {
        Copy-Item "manifests\schema-chrome.json" "$Platform\schema.json" -Force
        Write-Host "Copied schema.json" -ForegroundColor Gray
    }
}

# Copy PWA manifest
if (Test-Path "manifests\manifest-pwa.json") {
    Copy-Item "manifests\manifest-pwa.json" "$Platform\manifest-pwa.json" -Force
    Write-Host "Copied PWA manifest" -ForegroundColor Gray
}

Write-Host "Build completed successfully!" -ForegroundColor Green
Write-Host "Extension files are in the '$Platform' directory" -ForegroundColor Green
Write-Host ""
Write-Host "To load the extension:" -ForegroundColor Cyan
Write-Host "1. Open $Platform browser" -ForegroundColor Cyan
Write-Host "2. Go to extensions page (chrome://extensions/ or about:addons)" -ForegroundColor Cyan
Write-Host "3. Enable Developer mode" -ForegroundColor Cyan
Write-Host "4. Click 'Load unpacked' and select the '$Platform' folder" -ForegroundColor Cyan
