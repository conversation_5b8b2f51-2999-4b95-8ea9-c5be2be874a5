# 精确匹配功能测试指南

## 🎯 功能说明

已将匹配算法从模糊匹配修改为**精确匹配**：

- **精确匹配**：验证码的issuer或account字段中的域名必须与当前页面的域名**完全一致**才会显示
- **示例**：`<EMAIL>` 只会在访问 `kfms-pre-api.bbadev.cc` 时匹配

## 🧪 测试步骤

### 1. 重新加载扩展
1. 打开Chrome浏览器
2. 访问 `chrome://extensions/`
3. 找到Authenticator扩展
4. 点击"重新加载"按钮

### 2. 添加测试验证码
在扩展中添加以下测试验证码：

#### 测试验证码1
- **Issuer**: `<EMAIL>`
- **Account**: `test1`
- **Secret**: `JBSWY3DPEHPK3PXP`

#### 测试验证码2
- **Issuer**: `<EMAIL>`
- **Account**: `test2`
- **Secret**: `JBSWY3DPEHPK3PXP`

#### 测试验证码3
- **Issuer**: `GitHub`
- **Account**: `<EMAIL>`
- **Secret**: `JBSWY3DPEHPK3PXP`

#### 测试验证码4
- **Issuer**: `Google`
- **Account**: `<EMAIL>`
- **Secret**: `JBSWY3DPEHPK3PXP`

### 3. 测试精确匹配

#### 测试场景1：应该匹配
1. 访问包含 `kfms-pre-api.bbadev.cc` 的网站
2. 打开验证码扩展弹窗
3. **预期结果**：只显示测试验证码1（`<EMAIL>`）

#### 测试场景2：应该匹配（重点测试）
1. 访问 `https://dev-goc.bbadev.cc/login/index.html`
2. 打开验证码扩展弹窗
3. **预期结果**：显示测试验证码2（`<EMAIL>`）

#### 测试场景3：应该匹配
1. 访问 `github.com` 或 `www.github.com`
2. 打开验证码扩展弹窗
3. **预期结果**：显示测试验证码3（`<EMAIL>`）

#### 测试场景4：不应该匹配
1. 访问 `google.com`
2. 打开验证码扩展弹窗
3. **预期结果**：不显示测试验证码4（因为 `gmail.com` ≠ `google.com`）

#### 测试场景5：不应该匹配
1. 访问 `admin.kfms-pre-api.bbadev.cc`（子域名）
2. 打开验证码扩展弹窗
3. **预期结果**：不显示测试验证码1（因为子域名不完全匹配）

## 🔍 匹配规则详解

### 支持的格式
- `<EMAIL>` - 提取 `domain.com`
- `domain.com` - 直接使用 `domain.com`
- `https://domain.com` - 提取 `domain.com`

### 匹配条件
- 域名必须**完全一致**
- 不支持子域名匹配
- 不支持部分匹配
- 大小写不敏感

### 示例对比

| 验证码域名 | 当前页面 | 是否匹配 | 说明 |
|-----------|----------|----------|------|
| `<EMAIL>` | `dev-goc.bbadev.cc` | ✅ | 完全一致 |
| `example.com` | `example.com` | ✅ | 完全一致 |
| `example.com` | `www.example.com` | ❌ | 子域名不匹配 |
| `<EMAIL>` | `github.com` | ✅ | 提取域名后一致 |
| `<EMAIL>` | `google.com` | ❌ | 域名不一致 |
| `api.example.com` | `example.com` | ❌ | 子域名不匹配 |

## 🚀 自动填充测试

### 配置快捷键
1. 访问 `chrome://extensions/shortcuts/`
2. 为"Autofill the matched code"设置快捷键（推荐：`Ctrl+Shift+A`）

### 测试自动填充
1. 访问有验证码输入框的页面
2. 确保页面域名与某个验证码匹配
3. 按下配置的快捷键
4. **预期结果**：自动填入匹配的验证码

## 📝 注意事项

1. **精确匹配更严格**：只有域名完全一致才会匹配
2. **置顶验证码**：始终显示，不受匹配规则影响
3. **搜索功能**：可以覆盖过滤，手动查找验证码
4. **多个匹配**：如果有多个验证码匹配，会选择优先级最高的

## 🔧 故障排除

### 验证码不显示
1. 检查验证码的issuer或account字段是否包含正确的域名
2. 确认域名是否完全一致（不支持子域名）
3. 尝试使用搜索功能手动查找

### 自动填充不工作
1. 确认快捷键是否正确配置
2. 检查页面是否有验证码输入框
3. 确认当前页面是否有匹配的验证码

## 📊 测试结果记录

请在测试后记录结果：

- [ ] 测试场景1：精确匹配成功
- [ ] 测试场景2：GitHub匹配成功
- [ ] 测试场景3：Gmail不匹配（正确）
- [ ] 测试场景4：子域名不匹配（正确）
- [ ] 自动填充功能正常
- [ ] 置顶验证码始终显示
- [ ] 搜索功能正常
