<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>特定用例测试 - dev-goc.bbadev.cc</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-case {
            background: #f5f5f5;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .code-block {
            background: #e9ecef;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            margin: 5px 0;
        }
        .highlight {
            background: yellow;
            padding: 2px 4px;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <h1>特定用例测试：dev-goc.bbadev.cc</h1>
    
    <div class="test-case info">
        <h3>当前测试场景</h3>
        <p><strong>网页URL</strong>: https://dev-goc.bbadev.cc/login/index.html</p>
        <p><strong>验证码备注</strong>: <EMAIL></p>
        <p><strong>预期结果</strong>: <span class="highlight">应该匹配成功</span></p>
    </div>

    <div class="test-case">
        <h3>匹配分析</h3>
        <p><strong>页面域名</strong>: <code>dev-goc.bbadev.cc</code></p>
        <p><strong>验证码域名</strong>: <code>dev-goc.bbadev.cc</code> (从 <EMAIL> 提取)</p>
        <p><strong>匹配结果</strong>: ✅ 域名完全一致</p>
    </div>

    <div class="test-case success">
        <h3>测试步骤</h3>
        <ol>
            <li>在验证码扩展中添加验证码：
                <div class="code-block">
                    Issuer: <EMAIL><br>
                    Account: test<br>
                    Secret: JBSWY3DPEHPK3PXP
                </div>
            </li>
            <li>访问页面: <a href="https://dev-goc.bbadev.cc/login/index.html" target="_blank">https://dev-goc.bbadev.cc/login/index.html</a></li>
            <li>打开验证码扩展弹窗</li>
            <li>验证是否显示了匹配的验证码</li>
        </ol>
    </div>

    <div class="test-case">
        <h3>更多测试用例</h3>
        <table border="1" style="width: 100%; border-collapse: collapse;">
            <tr style="background: #f8f9fa;">
                <th style="padding: 8px;">验证码备注</th>
                <th style="padding: 8px;">当前页面</th>
                <th style="padding: 8px;">匹配结果</th>
            </tr>
            <tr>
                <td style="padding: 8px;"><EMAIL></td>
                <td style="padding: 8px;">dev-goc.bbadev.cc</td>
                <td style="padding: 8px; color: green;">✅ 匹配</td>
            </tr>
            <tr>
                <td style="padding: 8px;"><EMAIL></td>
                <td style="padding: 8px;">kfms-pre-api.bbadev.cc</td>
                <td style="padding: 8px; color: green;">✅ 匹配</td>
            </tr>
            <tr>
                <td style="padding: 8px;"><EMAIL></td>
                <td style="padding: 8px;">test.bbadev.cc</td>
                <td style="padding: 8px; color: green;">✅ 匹配</td>
            </tr>
            <tr>
                <td style="padding: 8px;"><EMAIL></td>
                <td style="padding: 8px;">admin.example.com</td>
                <td style="padding: 8px; color: red;">❌ 不匹配</td>
            </tr>
        </table>
    </div>

    <div class="test-case">
        <h3>匹配算法说明</h3>
        <p>精确匹配算法的工作原理：</p>
        <ol>
            <li><strong>提取域名</strong>: 从验证码备注中提取域名部分
                <ul>
                    <li><code><EMAIL></code> → <code>dev-goc.bbadev.cc</code></li>
                    <li><code>https://example.com</code> → <code>example.com</code></li>
                </ul>
            </li>
            <li><strong>获取页面域名</strong>: 从当前页面URL中获取域名
                <ul>
                    <li><code>https://dev-goc.bbadev.cc/login/index.html</code> → <code>dev-goc.bbadev.cc</code></li>
                </ul>
            </li>
            <li><strong>精确比较</strong>: 两个域名必须完全一致（忽略大小写）</li>
        </ol>
    </div>

    <div class="test-case info">
        <h3>如果匹配不成功，请检查：</h3>
        <ul>
            <li>验证码的issuer或account字段是否包含正确的域名</li>
            <li>域名拼写是否正确</li>
            <li>是否有多余的空格或特殊字符</li>
            <li>扩展是否已重新加载最新代码</li>
        </ul>
    </div>

    <script>
        // 显示当前页面信息
        document.addEventListener('DOMContentLoaded', function() {
            const currentInfo = document.createElement('div');
            currentInfo.className = 'test-case info';
            currentInfo.innerHTML = `
                <h3>当前页面信息</h3>
                <p><strong>完整URL</strong>: ${window.location.href}</p>
                <p><strong>域名</strong>: ${window.location.hostname}</p>
                <p><strong>协议</strong>: ${window.location.protocol}</p>
                <p><strong>路径</strong>: ${window.location.pathname}</p>
            `;
            document.body.appendChild(currentInfo);
        });

        // 完整的匹配逻辑测试
        function isExactHostMatched(entryText, siteHost) {
            if (!entryText || !siteHost) {
                return false;
            }

            const entryLower = entryText.toLowerCase();
            const hostLower = siteHost.toLowerCase();

            // 从entryText中提取域名部分
            const domainRegex = /(?:@|:\/\/)?([a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g;
            const matches = entryLower.match(domainRegex);

            if (matches) {
                for (const match of matches) {
                    const cleanDomain = match.replace(/^(@|:\/\/)/, '');
                    if (cleanDomain === hostLower) {
                        return true;
                    }
                }
            }

            // 备用匹配
            const exactMatchRegex = new RegExp('\\\\b' + hostLower.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&') + '\\\\b', 'i');
            return exactMatchRegex.test(entryLower);
        }

        // 测试用例
        const testCases = [
            {
                name: "您的用例",
                entryText: "<EMAIL>",
                siteHost: "dev-goc.bbadev.cc",
                expected: true
            },
            {
                name: "kfms-pre-api 用例",
                entryText: "<EMAIL>",
                siteHost: "kfms-pre-api.bbadev.cc",
                expected: true
            },
            {
                name: "GitHub 用例",
                entryText: "<EMAIL>",
                siteHost: "github.com",
                expected: true
            },
            {
                name: "不匹配用例",
                entryText: "<EMAIL>",
                siteHost: "google.com",
                expected: false
            }
        ];

        // 运行测试并显示结果
        function runTests() {
            console.log("=== 精确匹配逻辑测试 ===");

            const resultsDiv = document.createElement('div');
            resultsDiv.className = 'test-case';
            resultsDiv.innerHTML = '<h3>测试结果</h3>';

            let passedTests = 0;

            testCases.forEach((testCase, index) => {
                const result = isExactHostMatched(testCase.entryText, testCase.siteHost);
                const passed = result === testCase.expected;

                const resultText = `
                    <p><strong>${testCase.name}</strong>:
                    <span style="color: ${passed ? 'green' : 'red'}">
                        ${passed ? '✅ 通过' : '❌ 失败'}
                    </span></p>
                    <p style="margin-left: 20px; font-size: 0.9em;">
                        验证码: "${testCase.entryText}" → 页面: "${testCase.siteHost}" →
                        预期: ${testCase.expected}, 实际: ${result}
                    </p>
                `;

                resultsDiv.innerHTML += resultText;

                console.log(`${testCase.name}: ${passed ? '✅' : '❌'} (预期: ${testCase.expected}, 实际: ${result})`);

                if (passed) passedTests++;
            });

            resultsDiv.innerHTML += `<p><strong>总结: ${passedTests}/${testCases.length} 测试通过</strong></p>`;
            document.body.appendChild(resultsDiv);
        }

        // 页面加载完成后运行测试
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
