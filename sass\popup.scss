@import "ui";

// Structure

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

[v-cloak] {
  display: none;
}

body {
  width: 320px;
  height: 480px;
  transform-origin: left top;
  overflow: hidden;
  font-family: arial, "Microsoft YaHei";
  font-size: 16px;
  cursor: default;
  user-select: none;

  :focus {
    outline: auto;
  }
}

.hideoutline * {
  outline: none !important;
}

svg {
  pointer-events: none;
}

.icon {
  @include themify($themes) {
    fill: themed("grey-1");
  }
  vertical-align: middle;
  svg {
    height: 16px;
    width: 16px;
  }
}

#codeClipboard {
  position: absolute;
  top: -1000px;
}

// Header
.header {
  height: 38px;
  line-height: 38px;
  position: relative;
  text-align: center;
  font-size: 16px;
  @include themify($themes) {
    color: themed("black-1");
    background: themed("white-1");
    border-bottom: themed("grey-2") 1px solid;
  }

  .icon {
    @include hover-black;
    cursor: pointer;

    svg {
      vertical-align: sub;
    }
  }
}

#i-menu {
  position: absolute;
  left: 20px;
  bottom: 0;
}

#i-lock {
  position: absolute;
  left: 45px;
  bottom: 0;
}

#i-sync {
  position: absolute;
  left: 70px;
  bottom: 0;
  cursor: default;
  @include themify($themes) {
    fill: themed("grey-2");
  }

  &:hover {
    svg {
      fill: inherit;
    }
  }
}

#i-plus,
#i-qr {
  position: absolute;
  right: 45px;
  bottom: 0;
}

#i-edit {
  position: absolute;
  right: 20px;
  bottom: 0;
}

#i-close {
  position: absolute;
  left: 20px;
  bottom: 0;
}

// Search & Filter
.under-header {
  padding: 0 10px;
  margin-left: 10px;
  font-size: 12px;
  height: 24px;
  line-height: 24px;
  cursor: pointer;
  display: none;
}

#filter {
  @include themify($themes) {
    background: themed("yellow-2");
  }

  &:hover {
    @include themify($themes) {
      background: themed("yellow-1");
    }
  }
}

#search {
  position: relative;
  @include themify($themes) {
    background: themed("white-1");
    border: themed("grey-2") 1px solid;
    border-top: none;
  }

  &:hover {
    @include themify($themes) {
      background: themed("white-search");
    }
  }

  input {
    font-family: arial, "Microsoft YaHei";
    background: none;
    border: none;
    width: 100%;
    height: 100%;
    @include themify($themes) {
      color: themed("black-search");
    }
  }

  #searchHint {
    position: absolute;
    top: -1px;
    right: 0px;
    padding-right: 10px;
    display: grid;
    grid-template-rows: 4.5px 15px 4.5px;
    grid-template-columns: 15px;
  }

  #searchHintBorder {
    @include themify($themes) {
      color: themed("white-1");
      background: themed("grey-search");
    }
    text-align: center;
    border-radius: 1.5px;
    line-height: 16px;
    font-weight: bolder;
  }
}

// Codes
#codes {
  height: 442px;
  overflow-x: hidden;
  overflow-y: hidden;
  @include themify($themes) {
    background: themed("grey-background");
  }
  padding-right: 10px;

  .deleteAction {
    @include themify($themes) {
      @include icon-special(20px, themed("red-1"));
    }
    position: absolute;
    top: -10px;
    left: -10px;
    z-index: 10;
    display: none;
  }

  &:hover {
    padding-right: 0;
    overflow-y: scroll;
  }

  &.edit {
    .code {
      @include themify($themes) {
        color: themed("grey-2") !important;
      }
      user-select: none;
      cursor: default;
    }

    .issuer,
    .showqr,
    .showqr.hidden,
    .pin,
    .no-entry {
      display: none;
    }

    .issuerEdit,
    .movehandle,
    #add {
      display: block;
    }

    .deleteAction {
      display: block;
      cursor: pointer;
    }

    .sector,
    .counter {
      position: absolute;
      left: -1000px;
      opacity: 0;
    }
  }

  &.filter .entry[filtered],
  &.search .entry[notSearched] {
    height: 0;
    margin: 0;
    padding: 0;
    opacity: 0;
    border: none;
    overflow: hidden;
    position: absolute;
  }

  &.filter #filter,
  &.search #search {
    display: block;
  }

  &:not(.edit) {
    // Is this used?
    .entry[unencrypted="true"]:hover .warning {
      height: 24px;
    }

    .code.timeout:not(.hotp) {
      animation: twinkling 1s infinite ease-in-out;
    }
  }

  .no-entry {
    @include themify($themes) {
      color: themed("grey-1");
    }

    margin: 20px;
    text-align: center;

    svg {
      width: 48px;
      height: 48px;
      margin: 20px;
    }
  }
}

.entry {
  margin: 10px;
  margin-right: 0;
  padding: 10px;
  @include themify($themes) {
    border: themed("grey-2") 1px solid;
    background: themed("white-1");
  }
  border-radius: 2px;
  position: relative;
  display: block;
  cursor: pointer;

  .issuer {
    font-size: 12px;
    @include themify($themes) {
      color: themed("black-1");
    }
    width: 80%;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .code {
    font-size: 36px;
    @include themify($themes) {
      color: themed("blue-1");
    }
    width: 80%;
    user-select: text;
    font-family: "Droid Sans Mono";
  }

  .sector,
  .counter {
    width: 20px;
    height: 20px;
    position: absolute;
    right: 10px;
    bottom: 10px;
  }

  .sector {
    svg {
      width: 16px;
      height: 16px;
      margin: 2px;
    }

    circle {
      fill: none;
      transform: rotate(-90deg);
      transform-origin: 50% 50%;
      @include themify($themes) {
        stroke: themed("grey-1");
      }
      stroke-width: 8px;
      stroke-dasharray: 25.12;
      animation-name: timer;
      animation-iteration-count: infinite;
      animation-timing-function: linear;
    }
  }

  .counter {
    @include themify($themes) {
      @include icon-special(18px, themed("grey-1"));
    }
    text-align: center;
    cursor: pointer;

    .disabled {
      svg {
        @include themify($themes) {
          fill: themed("grey-2");
        }
      }
      cursor: default;
    }

    &:not(.disabled):hover {
      svg {
        @include themify($themes) {
          fill: themed("black-1");
        }
      }
    }
  }

  .issuerEdit {
    display: none;

    input {
      border: none;
      height: 14px;
      width: 70%;
      font-size: 12px;
      outline: none;
      @include themify($themes) {
        background: themed("grey-3");
      }
    }
  }

  .movehandle {
    @include themify($themes) {
      @include icon-special(24px, themed("grey-2"));
    }
    height: 98px;
    line-height: 98px;
    right: 10px;
    top: 0;
    position: absolute;
    cursor: move;
    display: none;
  }

  .showqr,
  .pin {
    @include themify($themes) {
      @include icon-special(20px, themed("grey-2"));
    }
    @include hover-black;
    right: 10px;
    top: 10px;
    position: absolute;
    cursor: pointer;
    opacity: 0;
  }

  .showqr {
    right: 35px;
  }

  &:hover {
    .showqr,
    .pin {
      opacity: 1;
    }

    .movehandle {
      svg {
        @include themify($themes) {
          fill: themed("black-1");
        }
      }
    }
  }
  /*
    // Is this used?
    &[dropOver="true"] {
        border: $grey-1 1px dashed;
    }

    // Is this used?
    &[unencrypted="true"] .warning {
        position: absolute;
        height: 0;
        line-height: 12px;
        font-size: 12px;
        padding: 0 10px;
        margin: 0 4px;
        width: 250px;
        bottom: 4px;
        left: 0;
        background: #EC6959;
        color: $white;
        cursor: pointer;
        overflow: hidden;
        border-radius: 2px;
        transition: height 0.2s;
    }
    */
}

.pinnedEntry {
  .pin {
    opacity: 1;
  }

  .movehandle {
    display: none !important;
  }
}

.no-copy {
  cursor: default;
}

#add {
  @extend .button;
  @include hover-black;
  margin-right: 0;
  line-height: 56px;
  display: none;
}

// Modals
#notification {
  position: absolute;
  left: 60px;
  top: -1000px;
  width: 200px;
  height: 60px;
  line-height: 60px;
  text-align: center;
  @include themify($themes) {
    background: themed("black-transparent");
    color: themed("white-1");
  }
  font-size: 20px;
  border-radius: 2px;

  &.fadein {
    top: 190px;
    animation: fadeshow 0.2s 1 ease-out;
  }

  &.fadeout {
    top: 190px;
    animation: fadehide 0.2s 1 ease-in;
  }
}

.message-box {
  position: absolute;
  width: 300px;
  padding: 10px;
  @include themify($themes) {
    color: themed("black-1");
    border: themed("grey-1") 1px solid;
    background: themed("white-1");
    box-shadow: 1px 1px 3px themed("grey-1");
  }
  border-radius: 2px;
  left: 10px;
  top: 150px;
  z-index: 1000;

  .button-small {
    margin-top: 10px;
    margin-bottom: 10px;
  }
}

.buttons {
  display: flex;
  justify-content: center;
  text-align: center;

  .button-small {
    display: inline-block;
    flex-basis: content;
    margin: 10px;
    padding: 5px 20px;
  }
}

#qr {
  width: 320px;
  height: 480px;
  top: -1000px;
  left: 0;
  position: absolute;
  z-index: 10;
  @include themify($themes) {
    background-color: themed("white-transparent");
  }
  background-repeat: no-repeat;
  background-position: center;

  canvas {
    display: none;
  }

  &.qrfadein {
    top: 0;
    animation: fadeshow 0.2s 1 ease-out;
  }

  &.qrfadeout {
    top: 0;
    animation: fadehide 0.2s 1 ease-in;
  }
}

#overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 900;
}

// Info
#info {
  position: absolute;
  height: 460px;
  width: 300px;
  padding: 10px;
  @include themify($themes) {
    color: themed("black-1");
    border: themed("grey-1");
    background: themed("white-1");
    box-shadow: 1px 1px 3px themed("grey-1");
  }
  left: 10px;
  top: -1000px;
  z-index: 100;

  #infoContent {
    height: 420px;
    overflow-y: auto;
    overflow-x: hidden;

    p {
      margin-bottom: 20px;
    }
  }

  #infoClose {
    @include hover-black;
    @include themify($themes) {
      @include icon-special(14px, themed("grey-1"));
    }
    height: 20px;
    width: 20px;
    cursor: pointer;
  }

  label {
    display: block;
    margin: 10px 0 0 10px;
  }

  .control-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .control-group select {
    width: 100px;
    padding: 5px 10px;
    @include themify($themes) {
      border: themed("grey-2") 1px solid;
      background: themed("white-1");
      color: themed("black-1");
    }
  }

  .control-group input[type="checkbox"] {
    position: relative;
    border: none;
    background: none;
    width: 34px;
    height: 16px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    outline: none;
  }

  .control-group input[type="checkbox"]::before {
    content: "";
    display: block;
    width: 28px;
    height: 12px;
    border-radius: 8px;
    background: grey;
    margin: 2px 3px;
    transition: all 80ms linear;
  }

  .control-group input[type="checkbox"]::after {
    content: "";
    display: block;
    position: absolute;
    width: 16px;
    height: 16px;
    border-radius: 10px;
    background: white;
    top: 0;
    left: 0;
    box-shadow: 0 0 2px black;
    transition: all 80ms linear;
  }

  .control-group input[type="checkbox"]:checked::before {
    background: #08c;
  }

  .control-group input[type="checkbox"]:checked::after {
    transform: matrix(1, 0, 0, 1, 18, 0);
  }

  .combo-label {
    display: flex;
    margin: 20px;
    margin-right: 0px;
    font-size: 16px;
  }

  .checkbox {
    margin: 20px;
  }

  select {
    margin: 20px;
    font-size: 12px;
  }

  a {
    text-decoration: none;
  }

  .button {
    display: block;
  }

  .text {
    display: block;
    margin: 10px 0 0 10px;
  }

  .warning {
    @include themify($themes) {
      color: themed("red-1");
    }
  }

  // Advisor
  .advisor {
    .show-all-insights {
      text-align: right;
      margin: 5px;
      margin-bottom: 20px;
      font-size: 12px;
    }

    .no-insight {
      @include themify($themes) {
        color: themed("grey-1");
      }

      margin: 20px;
    }

    .insight {
      font-size: 14px;
      border-left: 5px solid;
      padding-left: 5px;
      margin: 20px 5px 20px 0;

      h3 {
        margin-bottom: 5px;
      }

      p {
        margin-bottom: 5px !important;
      }

      .link {
        text-align: right;
        font-size: 12px;

        a:not(:first-child) {
          @include themify($themes) {
            border-left: 1px solid themed("grey-1");
          }
          margin-left: 10px;
          padding-left: 10px;
        }
      }
    }

    [level="danger"] {
      border-left-color: firebrick;
    }

    [level="warning"] {
      border-left-color: darkorange;
    }

    [level="info"] {
      border-left-color: #08c;
    }
  }

  // Security
  @mixin security-button($margin-left) {
    @extend .button-small;
    font-size: 12px;
    margin: 20px 100px;
    padding: 10px;
    display: inline-block;
    width: 80px;
    margin-left: $margin-left;
    margin-right: 0;
  }

  #security-save {
    @include security-button(40px);
  }

  #security-remove {
    @include security-button(30px);
  }

  .badInput {
    @include themify($themes) {
      input {
        border-color: themed("red-1");
      }
    }
  }

  &.fadein {
    top: 10px;
    animation: fadein 0.2s 1 ease-out;
  }

  &.fadeout {
    top: 110px;
    animation: fadeout 0.2s 1 ease-in;
  }

  &.show {
    top: 10px;
  }
}

// Menu
#menu {
  width: 320px;
  height: 480px;
  position: absolute;
  left: -1000px;
  top: 0;

  &.slidein {
    left: 0;
    animation: slidein 0.2s 1 ease-out;
    opacity: 1;
  }

  &.slideout {
    left: -55px;
    animation: slideout 0.2s 1 ease-in;
    opacity: 0;
  }

  #menuBody {
    overflow-y: auto;
    height: 442px;
    width: inherit;
    position: absolute;
    @include themify($themes) {
      background: themed("grey-background");
    }
  }

  .menuList {
    margin: 10px;
    border-radius: 2px;
    @include themify($themes) {
      border: themed("grey-2") 1px solid;
      background: themed("white-1");
    }

    p {
      position: relative;
      padding: 10px;
      font-size: 16px;
      cursor: pointer;
      display: grid;
      grid-template-columns: 30px auto;
      @include themify($themes) {
        border-bottom: themed("grey-2") 1px solid;
        color: themed("grey-1");
      }

      span {
        display: flex;
        align-items: center;

        svg {
          @include themify($themes) {
            fill: themed("grey-1");
          }
          height: 16px;
          width: 16px;
        }
      }

      &:hover {
        @include themify($themes) {
          background: themed("blue-menu");
          color: themed("black-1");
        }

        svg {
          @include themify($themes) {
            fill: themed("black-1");
          }
        }
      }

      &:last-child {
        border-bottom: none;
      }
    }
  }
}

#version {
  text-align: center;
  margin: 10px;
  @include themify($themes) {
    color: themed("grey-1");
  }
}

// Animations
@keyframes timer {
  to {
    stroke-dashoffset: -25.12;
  }
}

@keyframes twinkling {
  0% {
    color: #eea59c;
  }
  100% {
    color: #dd4b39;
  }
}

@keyframes flash {
  0%,
  33% {
    opacity: 0;
  }
  33.1%,
  100% {
    opacity: 1;
  }
}

@keyframes glint {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.15;
  }
}

@keyframes fadeshow {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes fadehide {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

@keyframes fadein {
  0% {
    opacity: 0;
    top: 110px;
  }
  100% {
    opacity: 1;
    top: 10px;
  }
}

@keyframes fadeout {
  0% {
    opacity: 1;
    top: 10px;
  }
  100% {
    opacity: 0;
    top: 110px;
  }
}

@keyframes slidein {
  0% {
    opacity: 0;
    left: -55px;
  }
  100% {
    opacity: 1;
    left: 0;
  }
}

@keyframes slideout {
  0% {
    opacity: 1;
    left: 0;
  }
  100% {
    opacity: 0;
    left: -55px;
  }
}

// Misc
@font-face {
  font-family: "Droid Sans Mono";
  font-style: normal;
  font-weight: 400;
  src: url(DroidSansMono.woff2) format("woff2");
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02C6, U+02DA, U+02DC,
    U+2000-206F, U+2074, U+20AC, U+2212, U+2215, U+E0FF, U+EFFD, U+F000;
}

.gu-mirror {
  display: none;
}

::-webkit-scrollbar {
  width: 10px;
  @include themify($themes) {
    background: themed("grey-3");
  }
}

::-webkit-scrollbar-thumb {
  @include themify($themes) {
    background-color: themed("grey-1");
    border: 2px solid themed("grey-3");
  }
  border-radius: 5px;
}

// Dark overrides
.theme-dark {
  #codes {
    &:not(.edit) {
      .code.timeout:not(.hotp) {
        animation: glint 1s infinite;
      }
    }
  }

  .message-box,
  #info {
    border: #ccc 1px solid;
    box-shadow: 1px 1px 3px black;
  }

  .entry .issuerEdit input {
    color: #ccc;
  }

  ::-webkit-scrollbar {
    background: #1e1e1e !important;
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.5) !important;
    border: 2px solid #1e1e1e !important;
  }
}

// Simple & Compact overrides
.theme-simple,
.theme-compact {
  .header {
    border-bottom: white 1px solid !important;

    &::after {
      content: "";
      display: block;
      margin: 0 20px -1px 20px;
      border-bottom: #eee 1px solid;
    }
  }

  #search {
    border-color: white !important;
  }

  .entry {
    border-color: white !important;
  }

  &:not(.edit) {
    .entry:hover {
      background: #f8f8f8;
    }
  }

  .control-group select {
    border-color: white !important;
  }

  .menuList {
    border-color: white !important;

    p {
      border-color: white !important;
    }
  }

  ::-webkit-scrollbar {
    background-color: white !important;
  }

  ::-webkit-scrollbar-thumb {
    border: 2px solid white !important;
  }

  .button {
    background: #f2f2f2 !important;
    border: #f2f2f2 1px solid !important;
    color: black !important;

    &:hover {
      background: #f8f8f8 !important;
      border: #f8f8f8 1px solid !important;
    }
  }

  #info {
    box-shadow: none !important;
  }

  .message-box {
    border: #ccc 1px solid;
    box-shadow: none !important;
  }
}

// Compact overrides
.theme-compact {
  #codes {
    &:not(.edit) {
      .entry {
        padding: 0 10px;
        margin: 0 0 0 10px;
      }
    }
  }

  .entry {
    .sector {
      bottom: 0;
    }

    .showqr,
    .pin {
      top: 0;
    }

    .code {
      font-size: 32px;
      margin-top: -5px;
      letter-spacing: 0.5rem;
    }
  }

  a.entry {
    border-bottom: #eee 1px solid !important;
  }

  .issuer.account {
    display: none;
  }
}

// Accessibility overrides
.theme-accessibility {
  select {
    border-color: white;
    color: white;
    background-color: black;
  }
  .entry {
    .issuerEdit {
      input {
        color: white;
        background: black;
        border: white 1px solid;
      }
    }

    .showqr,
    .pin {
      svg {
        fill: yellow;
      }

      &:hover {
        svg {
          fill: yellow;
        }
      }
    }
  }

  #menu {
    .menuList {
      p:hover {
        color: black;

        svg {
          fill: black;
        }
      }
    }
  }

  .header {
    .icon {
      svg {
        fill: yellow;
      }

      &#i-sync {
        svg {
          fill: white;
        }

        &:hover {
          svg {
            fill: white;
          }
        }
      }

      &:hover {
        svg {
          fill: yellow;
        }
      }
    }
  }

  #codes {
    &:not(.edit) {
      .code.timeout:not(.hotp) {
        animation: flash 1s infinite;
      }
    }
  }

  #info {
    border: 1px solid white;
    box-shadow: none;
  }

  #info .control-group input[type="checkbox"]::before {
    background: none;
    border: 2px solid white;
    box-sizing: border-box;
  }

  #info .control-group input[type="checkbox"]::after {
    border: 2px solid black;
    margin: -2px;
    box-shadow: none;
  }

  #info .control-group input[type="checkbox"]:checked::before {
    background: white;
  }

  .input {
    color: black;
  }

  #notification {
    &.fadein {
      opacity: 1;
      animation: none;
    }

    &.fadeout {
      opacity: 0;
      animation: none;
    }
  }

  ::-webkit-scrollbar {
    background: black !important;
  }

  ::-webkit-scrollbar-thumb {
    background-color: white !important;
    border: 2px solid black !important;
  }
}

// Flat overrides
.theme-flat {
  .header {
    color: black;
    background: white;
    border-bottom: #f5f4f7 1px solid;
  }

  #codes {
    background: #fcfbff;

    .entry {
      border: #f5f4f7 1px solid;
      border-radius: 8px;
    }
  }

  #menu {
    #menuBody {
      background: #fcfbff;

      .menuList {
        margin: 10px;
        border-radius: 8px;
        border: #f5f4f7 1px solid;
        background: white;

        p {
          color: #727272;

          &:not(:last-child) {
            border-bottom: #f5f4f7 1px solid;
          }

          span svg {
            fill: #727272;
          }
        }
      }
    }

    #version {
      bottom: 0px;
      position: relative;
      margin: 10px auto;
      width: 100%;
      color: #727272;
      font-size: 0.9em;
    }

    #info {
      border-radius: 8px;
      border: #f5f4f7 1px solid;

      .control-group {
        border-bottom: #f5f4f7 1px solid;
      }
    }
  }
}
