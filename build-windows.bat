@echo off
setlocal enabledelayedexpansion

:: Windows batch build script for Authenticator Extension
:: Usage: build-windows.bat [chrome|firefox|edge]

set PLATFORM=%1
if "%PLATFORM%"=="" set PLATFORM=chrome

echo Building Authenticator Extension for %PLATFORM%...

:: Clean old build files
echo Cleaning old build files...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist css rmdir /s /q css
if exist chrome rmdir /s /q chrome
if exist firefox rmdir /s /q firefox
if exist edge rmdir /s /q edge

:: Check credentials (warning only)
findstr "client_id: \"\"" src\models\credentials.ts >nul
if !errorlevel! equ 0 (
    echo Warning: Missing API credentials in credentials.ts
    echo This is OK for development, but you'll need real credentials for production
)

:: Run webpack
echo Running webpack...
call npx webpack --config webpack.config.js
if !errorlevel! neq 0 (
    echo Error: Webpack build failed
    exit /b 1
)

:: Compile SASS
echo Compiling SASS...
call npx sass sass:css
if !errorlevel! neq 0 (
    echo Error: SASS compilation failed
    exit /b 1
)

:: Copy additional CSS files
if exist css (
    copy sass\DroidSansMono.woff2 css\ >nul
    copy sass\mocha.css css\ >nul
)

:: Create platform-specific build
echo Creating %PLATFORM% build...

:: Create platform directory
mkdir %PLATFORM% 2>nul

:: Copy core files
echo Copying core files...
if exist dist xcopy dist %PLATFORM%\dist\ /e /i /y >nul
if exist css xcopy css %PLATFORM%\css\ /e /i /y >nul
if exist images xcopy images %PLATFORM%\images\ /e /i /y >nul
if exist _locales xcopy _locales %PLATFORM%\_locales\ /e /i /y >nul
if exist LICENSE copy LICENSE %PLATFORM%\ >nul
if exist view xcopy view %PLATFORM%\view\ /e /i /y >nul

:: Copy appropriate manifest
set MANIFEST_FILE=manifests\manifest-%PLATFORM%.json
if exist "%MANIFEST_FILE%" (
    copy "%MANIFEST_FILE%" %PLATFORM%\manifest.json >nul
    echo Copied manifest for %PLATFORM%
) else (
    echo Error: Manifest file %MANIFEST_FILE% not found
    exit /b 1
)

:: Copy schema for Chrome/Edge
if "%PLATFORM%"=="chrome" (
    if exist manifests\schema-chrome.json copy manifests\schema-chrome.json %PLATFORM%\schema.json >nul
)
if "%PLATFORM%"=="edge" (
    if exist manifests\schema-chrome.json copy manifests\schema-chrome.json %PLATFORM%\schema.json >nul
)

:: Copy PWA manifest
if exist manifests\manifest-pwa.json copy manifests\manifest-pwa.json %PLATFORM%\manifest-pwa.json >nul

echo.
echo Build completed successfully!
echo Extension files are in the '%PLATFORM%' directory
echo.
echo To load the extension:
echo 1. Open %PLATFORM% browser
echo 2. Go to extensions page (chrome://extensions/ or about:addons)
echo 3. Enable Developer mode
echo 4. Click 'Load unpacked' and select the '%PLATFORM%' folder

pause
