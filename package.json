{"name": "authenticator-extension", "version": "0.1.0", "description": "Authenticator generates 2-Step Verification codes in your browser.", "scripts": {"compile": "bash scripts/build.sh firefox", "dev:chrome": "npm run pretest && webpack --config ./webpack.watch.js", "chrome": "bash scripts/build.sh chrome", "firefox": "bash scripts/build.sh firefox", "edge": "bash scripts/build.sh edge", "prod": "bash scripts/build.sh prod", "pretest": "bash scripts/build.sh test", "test": "node scripts/test-runner.js"}, "repository": {"type": "git", "url": "git+https://github.com/Authenticator-Extension/Authenticator.git"}, "author": "Authenticator Extension", "license": "MIT", "bugs": {"url": "https://github.com/Authenticator-Extension/Authenticator/issues"}, "homepage": "https://github.com/Authenticator-Extension/Authenticator#readme", "devDependencies": {"@types/argon2-browser": "^1.18.1", "@types/chai": "^4.2.14", "@types/chrome": "^0.0.266", "@types/crypto-js": "^4.1.1", "@types/mocha": "^10.0.6", "@types/sinon": "^17.0.2", "@types/sinon-chai": "^3.2.12", "@types/sinon-chrome": "^2.2.10", "@typescript-eslint/eslint-plugin": "^7.15.0", "@typescript-eslint/parser": "^7.15.0", "@vue/test-utils": "^1.1.1", "base64-loader": "^1.0.0", "buffer": "^6.0.3", "chai": "^4.2.0", "crypto-js": "^4.1.1", "eslint": "^8.56.0", "fork-ts-checker-webpack-plugin": "^6.5.3", "lodash": "^4.17.21", "mocha": "^10.2.0", "npm-license-generator": "^2.0.0", "nyc": "^15.1.0", "prettier": "2.2.1", "process": "^0.11.10", "puppeteer": "^22.11.2", "sass": "^1.26.11", "sinon": "^17.0.1", "sinon-chai": "^3.7.0", "sinon-chrome": "^3.0.1", "stream-browserify": "^3.0.0", "ts-loader": "^9.0.0", "typescript": "^5.0.0", "url-loader": "^4.0.0", "util": "^0.12.5", "vue-loader": "^15.10.1", "vue-svg-loader": "^0.16.0", "vue-template-compiler": "^2.7.16", "webpack": "^5.94.0", "webpack-cli": "^5.1.4", "webpack-merge": "^5.0.0"}, "dependencies": {"@types/lodash": "^4.14.166", "argon2-browser": "^1.18.0", "jsqr": "^1.3.1", "node-gost-crypto": "^1.0.2", "qrcode-generator": "^1.4.4", "qrcode-reader": "^1.0.4", "vue": "^2.7.16", "vue2-dragula": "^2.5.4", "vuex": "^3.4.0"}}