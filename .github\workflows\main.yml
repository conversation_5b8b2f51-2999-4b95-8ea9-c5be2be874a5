name: CI

on: [push, pull_request]

jobs:
  style:
    runs-on: ubuntu-latest
    name: Style checks

    steps:
      - uses: actions/checkout@v2

      - name: Setup Node.js environment
        uses: actions/setup-node@v2.1.2

      - name: Install dependencies
        run: |
          npm i prettier

      - name: Prettier
        run: ./node_modules/prettier/bin-prettier.js --check ./src/* ./src/**/* ./src/**/**/* ./sass/*.scss

  build:
    runs-on: ubuntu-latest
    name: Build ${{ matrix.platform }}

    strategy:
      matrix:
        platform: ["chrome", "firefox"]

    steps:
      - uses: actions/checkout@v2

      - name: Setup Node.js environment
        uses: actions/setup-node@v2.1.2

      - name: Install dependencies
        run: npm ci

      - name: Build
        run: npm run ${{ matrix.platform }}
  run-tests:
    runs-on: ubuntu-latest
    name: Run tests
    needs: [build]

    steps:
      - uses: actions/checkout@v2

      - name: Setup Node.js environment
        uses: actions/setup-node@v2.1.2

      - name: Install dependencies
        run: npm ci
        env:
          PUPPETEER_SKIP_CHROMIUM_DOWNLOAD: "true"

      - name: Test code
        uses: mymindstorm/puppeteer-headful@8f745c770f7f4c0f9f332d7c43a775f90e53779a
        with:
          args: npm test
