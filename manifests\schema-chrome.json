{"type": "object", "properties": {"disableInstallHelp": {"title": "Disable opening help page on install", "description": "If set to true, then help page will not be opened on install.", "type": "boolean"}, "disableBackup": {"title": "Disable 3rd party backup", "description": "If set to true, then 3rd party backup options will be hidden. If 3rd party backup is already configured for a user this will not stop it.", "type": "boolean"}, "disableExport": {"title": "Disable import / export menu", "description": "If set to true, then export buttons will be hidden.", "type": "boolean"}, "storageArea": {"title": "Storage area", "description": "Set to 'sync' or 'local'. If set will force user to use specified storage area. This setting will not check if a user is currently using another storage space and may hide data.", "type": "string"}, "feedbackURL": {"title": "Feedback URL", "description": "Change the URL the feedback button opens.", "type": "string"}, "enforcePassword": {"title": "Enforce password", "description": "If set to true, then user will be prompted to set a password before adding an account (if none set) and the remove password button will be hidden.", "type": "boolean"}, "enforceAutolock": {"title": "Enforce autolock", "description": "If any value is set, then the user will not be able to change the autolock setting. Set to a number in minutes.", "type": "number"}, "passwordPolicy": {"title": "Password policy", "description": "A regular expression to test if the password meets the security requirements. No slashes are needed (e.g. use [A-Z]+, but not use /[A-Z]+/).", "type": "string"}, "passwordPolicyHint": {"title": "Password policy hint", "description": "Hint to show if the password doesn't meet the security requirements.", "type": "string"}}}