<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强匹配功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-case {
            background: #f5f5f5;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .expected {
            color: #28a745;
            font-weight: bold;
        }
        .code-block {
            background: #e9ecef;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <h1>验证码增强匹配功能测试页面</h1>
    
    <h2>功能说明</h2>
    <p>本次更新增强了验证码匹配功能，支持以下特性：</p>
    <ul>
        <li><strong>自动填入验证码</strong>：根据当前页面自动选择最匹配的验证码并填入</li>
        <li><strong>智能过滤显示</strong>：在验证码列表中只显示与当前页面相关的验证码</li>
        <li><strong>模糊匹配算法</strong>：支持更灵活的域名匹配规则</li>
    </ul>

    <h2>测试用例</h2>
    
    <div class="test-case">
        <h3>测试用例 1：完整域名匹配</h3>
        <p><strong>当前页面</strong>：kfms-pre-api.bbadev.cc</p>
        <p><strong>验证码备注</strong>：<EMAIL></p>
        <p class="expected">预期结果：应该匹配成功</p>
    </div>

    <div class="test-case">
        <h3>测试用例 2：子域名匹配</h3>
        <p><strong>当前页面</strong>：admin.example.com</p>
        <p><strong>验证码备注</strong>：example.com</p>
        <p class="expected">预期结果：应该匹配成功</p>
    </div>

    <div class="test-case">
        <h3>测试用例 3：主域名匹配</h3>
        <p><strong>当前页面</strong>：www.github.com</p>
        <p><strong>验证码备注</strong>：GitHub</p>
        <p class="expected">预期结果：应该匹配成功</p>
    </div>

    <div class="test-case">
        <h3>测试用例 4：Email格式匹配</h3>
        <p><strong>当前页面</strong>：mail.google.com</p>
        <p><strong>验证码备注</strong>：<EMAIL></p>
        <p class="expected">预期结果：应该匹配成功（gmail与google相关）</p>
    </div>

    <h2>使用方法</h2>
    <ol>
        <li>在浏览器中打开任意网站</li>
        <li>点击验证码扩展图标打开弹窗</li>
        <li>观察验证码列表是否正确过滤显示相关条目</li>
        <li>使用快捷键 <code>Ctrl+Shift+A</code>（或在扩展设置中配置的快捷键）测试自动填入功能</li>
    </ol>

    <h2>技术实现</h2>
    <div class="code-block">
        主要修改的文件：
        - src/utils.ts: 增强了isMatchedEntry函数，添加模糊匹配逻辑
        - src/background.ts: 改进了autofill功能，支持多个匹配时选择最佳匹配
        - src/popup.ts: 添加了标签页变化监听，实时更新匹配状态
        - src/store/Accounts.ts: 添加了updateSiteName mutation
        - src/components/Popup/EntryComponent.vue: 支持filtered和notSearched属性
    </div>

    <h2>匹配优先级</h2>
    <p>当有多个验证码匹配当前页面时，系统会按以下优先级选择：</p>
    <ol>
        <li>置顶(pinned)的验证码 (+100分)</li>
        <li>精确域名匹配 (+80分)</li>
        <li>完整域名包含匹配 (+50分)</li>
        <li>Account字段域名匹配 (+40分)</li>
        <li>主域名匹配 (+30分)</li>
        <li>Account字段主域名匹配 (+20分)</li>
    </ol>

    <script>
        // 显示当前页面信息
        document.addEventListener('DOMContentLoaded', function() {
            const currentInfo = document.createElement('div');
            currentInfo.className = 'test-case';
            currentInfo.innerHTML = `
                <h3>当前页面信息</h3>
                <p><strong>URL</strong>: ${window.location.href}</p>
                <p><strong>域名</strong>: ${window.location.hostname}</p>
                <p><strong>标题</strong>: ${document.title}</p>
            `;
            document.body.insertBefore(currentInfo, document.querySelector('h2'));
        });
    </script>
</body>
</html>
