@import "ui";

[v-cloak] {
  display: none;
}

* {
  font-family: arial, "Microsoft YaHei";
}

p {
  font-size: 16px;
}

#import {
  width: 900px;
  position: relative;
  margin: 0 auto;
}

#import_info {
  margin: 10px 20px 20px 20px;
}

.import_tab {
  text-align: center;
  font-size: 0;

  input {
    display: none;

    &:checked + label {
      background: #eee;
    }
  }

  label {
    width: 250px;
    height: 50px;
    font-size: 18px;
    text-align: center;
    display: inline-grid;
    align-items: center;
    margin: 20px;
    cursor: pointer;
    border-radius: 2px;

    &:hover {
      background: #eee;
    }
  }
}

button,
.import_file label {
  display: inline-grid;
  width: 260px !important;
  height: 60px;
  border: #ccc 1px solid;
  background: white;
  border-radius: 2px;
  position: relative;
  text-align: center;
  align-items: center;
  font-size: 16px;
  color: gray;
  cursor: pointer;
  outline: none;
  margin-left: 0px !important;

  &:hover {
    color: black;
  }
}

.import_file {
  text-align: center;

  input {
    display: none;
  }
}

.import_encrypted {
  margin-bottom: 20px;

  input {
    margin-left: 0;
  }
}

.import_code {
  float: left;
  margin-left: 30px;
  margin-right: 40px;

  textarea {
    width: 250px;
    height: 400px;
    padding: 10px;
    outline: none;
    resize: none;
    box-sizing: border-box;
  }
}

.error_password {
  font-size: 18px;
  color: gray;
  text-align: center;
}

.import_passphrase input,
.import_file_passphrase_input input {
  padding: 10px;
  margin-bottom: 20px;
  width: 250px;
  border: #ccc 1px solid;
  background: white;
  outline: none;
}

.import_file_passphrase {
  display: grid;
  justify-content: center;
}

.import_file_passphrase_input {
  display: inline-grid;
  grid-template-rows: min-content min-content;
}
